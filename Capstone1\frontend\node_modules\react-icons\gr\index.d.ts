// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const GrAccessibility: IconType;
export declare const GrAchievement: IconType;
export declare const GrAction: IconType;
export declare const GrActions: IconType;
export declare const GrAd: IconType;
export declare const GrAddCircle: IconType;
export declare const GrAdd: IconType;
export declare const GrAdobeCreativeCloud: IconType;
export declare const GrAed: IconType;
export declare const GrAggregate: IconType;
export declare const GrAidOption: IconType;
export declare const GrAid: IconType;
export declare const GrAlarm: IconType;
export declare const GrAlert: IconType;
export declare const GrAmazon: IconType;
export declare const GrAmex: IconType;
export declare const GrAnalytics: IconType;
export declare const GrAnchor: IconType;
export declare const GrAndroid: IconType;
export declare const GrAnnounce: IconType;
export declare const GrAppleAppStore: IconType;
export declare const GrAppleMusic: IconType;
export declare const GrApplePodcasts: IconType;
export declare const GrApple: IconType;
export declare const GrAppsRounded: IconType;
export declare const GrApps: IconType;
export declare const GrArchive: IconType;
export declare const GrArchlinux: IconType;
export declare const GrArticle: IconType;
export declare const GrAruba: IconType;
export declare const GrAscend: IconType;
export declare const GrAscending: IconType;
export declare const GrAssistListening: IconType;
export declare const GrAtm: IconType;
export declare const GrAttachment: IconType;
export declare const GrAttraction: IconType;
export declare const GrBaby: IconType;
export declare const GrBackTen: IconType;
export declare const GrBarChart: IconType;
export declare const GrBar: IconType;
export declare const GrBasket: IconType;
export declare const GrBeacon: IconType;
export declare const GrBike: IconType;
export declare const GrBitcoin: IconType;
export declare const GrBladesHorizontal: IconType;
export declare const GrBladesVertical: IconType;
export declare const GrBlockQuote: IconType;
export declare const GrBlog: IconType;
export declare const GrBluetooth: IconType;
export declare const GrBold: IconType;
export declare const GrBook: IconType;
export declare const GrBookmark: IconType;
export declare const GrBottomCorner: IconType;
export declare const GrBraille: IconType;
export declare const GrBriefcase: IconType;
export declare const GrBrush: IconType;
export declare const GrBucket: IconType;
export declare const GrBug: IconType;
export declare const GrBundle: IconType;
export declare const GrBus: IconType;
export declare const GrBusinessService: IconType;
export declare const GrCafeteria: IconType;
export declare const GrCalculator: IconType;
export declare const GrCalendar: IconType;
export declare const GrCamera: IconType;
export declare const GrCapacity: IconType;
export declare const GrCar: IconType;
export declare const GrCaretDownFill: IconType;
export declare const GrCaretDown: IconType;
export declare const GrCaretLeftFill: IconType;
export declare const GrCaretNext: IconType;
export declare const GrCaretPrevious: IconType;
export declare const GrCaretRightFill: IconType;
export declare const GrCaretUpFill: IconType;
export declare const GrCaretUp: IconType;
export declare const GrCart: IconType;
export declare const GrCatalogOption: IconType;
export declare const GrCatalog: IconType;
export declare const GrCentos: IconType;
export declare const GrCertificate: IconType;
export declare const GrChannel: IconType;
export declare const GrChapterAdd: IconType;
export declare const GrChapterNext: IconType;
export declare const GrChapterPrevious: IconType;
export declare const GrChatOption: IconType;
export declare const GrChat: IconType;
export declare const GrCheckboxSelected: IconType;
export declare const GrCheckbox: IconType;
export declare const GrCheckmark: IconType;
export declare const GrChrome: IconType;
export declare const GrCircleAlert: IconType;
export declare const GrCircleInformation: IconType;
export declare const GrCirclePlay: IconType;
export declare const GrCircleQuestion: IconType;
export declare const GrClearOption: IconType;
export declare const GrClear: IconType;
export declare const GrCli: IconType;
export declare const GrClipboard: IconType;
export declare const GrClock: IconType;
export declare const GrClone: IconType;
export declare const GrClose: IconType;
export declare const GrClosedCaption: IconType;
export declare const GrCloudComputer: IconType;
export declare const GrCloudDownload: IconType;
export declare const GrCloudSoftware: IconType;
export declare const GrCloudUpload: IconType;
export declare const GrCloud: IconType;
export declare const GrCloudlinux: IconType;
export declare const GrCluster: IconType;
export declare const GrCoatCheck: IconType;
export declare const GrCodeSandbox: IconType;
export declare const GrCode: IconType;
export declare const GrCodepen: IconType;
export declare const GrCoffee: IconType;
export declare const GrColumns: IconType;
export declare const GrCommand: IconType;
export declare const GrCompare: IconType;
export declare const GrCompass: IconType;
export declare const GrCompliance: IconType;
export declare const GrConfigure: IconType;
export declare const GrConnect: IconType;
export declare const GrConnectivity: IconType;
export declare const GrConsole: IconType;
export declare const GrContactInfo: IconType;
export declare const GrContact: IconType;
export declare const GrContract: IconType;
export declare const GrCopy: IconType;
export declare const GrCpu: IconType;
export declare const GrCreativeCommons: IconType;
export declare const GrCreditCard: IconType;
export declare const GrCss3: IconType;
export declare const GrCube: IconType;
export declare const GrCubes: IconType;
export declare const GrCurrency: IconType;
export declare const GrCursor: IconType;
export declare const GrCut: IconType;
export declare const GrCycle: IconType;
export declare const GrDashboard: IconType;
export declare const GrDatabase: IconType;
export declare const GrDebian: IconType;
export declare const GrDeliver: IconType;
export declare const GrDeploy: IconType;
export declare const GrDescend: IconType;
export declare const GrDescending: IconType;
export declare const GrDesktop: IconType;
export declare const GrDetach: IconType;
export declare const GrDevice: IconType;
export declare const GrDiamond: IconType;
export declare const GrDirections: IconType;
export declare const GrDisabledOutline: IconType;
export declare const GrDisc: IconType;
export declare const GrDislikeFill: IconType;
export declare const GrDislike: IconType;
export declare const GrDocker: IconType;
export declare const GrDocumentCloud: IconType;
export declare const GrDocumentConfig: IconType;
export declare const GrDocumentCsv: IconType;
export declare const GrDocumentDownload: IconType;
export declare const GrDocumentExcel: IconType;
export declare const GrDocumentImage: IconType;
export declare const GrDocumentLocked: IconType;
export declare const GrDocumentMissing: IconType;
export declare const GrDocumentNotes: IconType;
export declare const GrDocumentOutlook: IconType;
export declare const GrDocumentPdf: IconType;
export declare const GrDocumentPerformance: IconType;
export declare const GrDocumentPpt: IconType;
export declare const GrDocumentRtf: IconType;
export declare const GrDocumentSound: IconType;
export declare const GrDocumentStore: IconType;
export declare const GrDocumentTest: IconType;
export declare const GrDocumentText: IconType;
export declare const GrDocumentThreat: IconType;
export declare const GrDocumentTime: IconType;
export declare const GrDocumentTransfer: IconType;
export declare const GrDocumentTxt: IconType;
export declare const GrDocumentUpdate: IconType;
export declare const GrDocumentUpload: IconType;
export declare const GrDocumentUser: IconType;
export declare const GrDocumentVerified: IconType;
export declare const GrDocumentVideo: IconType;
export declare const GrDocumentWindows: IconType;
export declare const GrDocumentWord: IconType;
export declare const GrDocumentZip: IconType;
export declare const GrDocument: IconType;
export declare const GrDomain: IconType;
export declare const GrDos: IconType;
export declare const GrDown: IconType;
export declare const GrDownloadOption: IconType;
export declare const GrDownload: IconType;
export declare const GrDrag: IconType;
export declare const GrDrawer: IconType;
export declare const GrDribbble: IconType;
export declare const GrDriveCage: IconType;
export declare const GrDropbox: IconType;
export declare const GrDuplicate: IconType;
export declare const GrDxc: IconType;
export declare const GrEbay: IconType;
export declare const GrEdge: IconType;
export declare const GrEdit: IconType;
export declare const GrEject: IconType;
export declare const GrElevator: IconType;
export declare const GrEmergency: IconType;
export declare const GrEmoji: IconType;
export declare const GrEmptyCircle: IconType;
export declare const GrErase: IconType;
export declare const GrEscalator: IconType;
export declare const GrExpand: IconType;
export declare const GrEzmeral: IconType;
export declare const GrFacebookOption: IconType;
export declare const GrFacebook: IconType;
export declare const GrFanOption: IconType;
export declare const GrFan: IconType;
export declare const GrFastForward: IconType;
export declare const GrFavorite: IconType;
export declare const GrFedora: IconType;
export declare const GrFigma: IconType;
export declare const GrFilter: IconType;
export declare const GrFingerPrint: IconType;
export declare const GrFireball: IconType;
export declare const GrFirefox: IconType;
export declare const GrFirewall: IconType;
export declare const GrFlagFill: IconType;
export declare const GrFlag: IconType;
export declare const GrFlows: IconType;
export declare const GrFolderCycle: IconType;
export declare const GrFolderOpen: IconType;
export declare const GrFolder: IconType;
export declare const GrFormAdd: IconType;
export declare const GrFormAttachment: IconType;
export declare const GrFormCalendar: IconType;
export declare const GrFormCheckmark: IconType;
export declare const GrFormClock: IconType;
export declare const GrFormClose: IconType;
export declare const GrFormCut: IconType;
export declare const GrFormDown: IconType;
export declare const GrFormEdit: IconType;
export declare const GrFormFilter: IconType;
export declare const GrFormFolder: IconType;
export declare const GrFormLocation: IconType;
export declare const GrFormLock: IconType;
export declare const GrFormNextLink: IconType;
export declare const GrFormNext: IconType;
export declare const GrFormPin: IconType;
export declare const GrFormPreviousLink: IconType;
export declare const GrFormPrevious: IconType;
export declare const GrFormRefresh: IconType;
export declare const GrFormSchedule: IconType;
export declare const GrFormSearch: IconType;
export declare const GrFormSubtract: IconType;
export declare const GrFormTrash: IconType;
export declare const GrFormUp: IconType;
export declare const GrFormUpload: IconType;
export declare const GrFormViewHide: IconType;
export declare const GrFormView: IconType;
export declare const GrForwardTen: IconType;
export declare const GrFreebsd: IconType;
export declare const GrGallery: IconType;
export declare const GrGamepad: IconType;
export declare const GrGateway: IconType;
export declare const GrGatsbyjs: IconType;
export declare const GrGem: IconType;
export declare const GrGift: IconType;
export declare const GrGithub: IconType;
export declare const GrGlobe: IconType;
export declare const GrGolang: IconType;
export declare const GrGooglePay: IconType;
export declare const GrGooglePlay: IconType;
export declare const GrGooglePlus: IconType;
export declare const GrGoogleWallet: IconType;
export declare const GrGoogle: IconType;
export declare const GrGraphQl: IconType;
export declare const GrGremlin: IconType;
export declare const GrGrid: IconType;
export declare const GrGrommet: IconType;
export declare const GrGroup: IconType;
export declare const GrGrow: IconType;
export declare const GrHadoop: IconType;
export declare const GrHalt: IconType;
export declare const GrHelpBook: IconType;
export declare const GrHelpOption: IconType;
export declare const GrHelp: IconType;
export declare const GrHeroku: IconType;
export declare const GrHide: IconType;
export declare const GrHistory: IconType;
export declare const GrHomeOption: IconType;
export declare const GrHomeRounded: IconType;
export declare const GrHome: IconType;
export declare const GrHorton: IconType;
export declare const GrHostMaintenance: IconType;
export declare const GrHost: IconType;
export declare const GrHp: IconType;
export declare const GrHpeLabs: IconType;
export declare const GrHpe: IconType;
export declare const GrHpi: IconType;
export declare const GrHtml5: IconType;
export declare const GrIceCream: IconType;
export declare const GrImage: IconType;
export declare const GrImpact: IconType;
export declare const GrInProgress: IconType;
export declare const GrInbox: IconType;
export declare const GrIndicator: IconType;
export declare const GrInfo: IconType;
export declare const GrInherit: IconType;
export declare const GrInsecure: IconType;
export declare const GrInspect: IconType;
export declare const GrInstagram: IconType;
export declare const GrInstallOption: IconType;
export declare const GrInstall: IconType;
export declare const GrIntegration: IconType;
export declare const GrInternetExplorer: IconType;
export declare const GrItalic: IconType;
export declare const GrIteration: IconType;
export declare const GrJava: IconType;
export declare const GrJs: IconType;
export declare const GrKey: IconType;
export declare const GrKeyboard: IconType;
export declare const GrKubernetes: IconType;
export declare const GrLanguage: IconType;
export declare const GrLastfm: IconType;
export declare const GrLaunch: IconType;
export declare const GrLayer: IconType;
export declare const GrLicense: IconType;
export declare const GrLikeFill: IconType;
export declare const GrLike: IconType;
export declare const GrLineChart: IconType;
export declare const GrLinkBottom: IconType;
export declare const GrLinkDown: IconType;
export declare const GrLinkNext: IconType;
export declare const GrLinkPrevious: IconType;
export declare const GrLinkTop: IconType;
export declare const GrLinkUp: IconType;
export declare const GrLink: IconType;
export declare const GrLinkedinOption: IconType;
export declare const GrLinkedin: IconType;
export declare const GrList: IconType;
export declare const GrLocal: IconType;
export declare const GrLocationPin: IconType;
export declare const GrLocation: IconType;
export declare const GrLock: IconType;
export declare const GrLogin: IconType;
export declare const GrLogout: IconType;
export declare const GrLounge: IconType;
export declare const GrMagic: IconType;
export declare const GrMailOption: IconType;
export declare const GrMail: IconType;
export declare const GrMandriva: IconType;
export declare const GrManual: IconType;
export declare const GrMapLocation: IconType;
export declare const GrMap: IconType;
export declare const GrMastercard: IconType;
export declare const GrMedium: IconType;
export declare const GrMemory: IconType;
export declare const GrMenu: IconType;
export declare const GrMeta: IconType;
export declare const GrMicrofocus: IconType;
export declare const GrMicrophone: IconType;
export declare const GrMoney: IconType;
export declare const GrMonitor: IconType;
export declare const GrMonospace: IconType;
export declare const GrMoon: IconType;
export declare const GrMoreVertical: IconType;
export declare const GrMore: IconType;
export declare const GrMouse: IconType;
export declare const GrMultimedia: IconType;
export declare const GrMultiple: IconType;
export declare const GrMusic: IconType;
export declare const GrMysql: IconType;
export declare const GrNavigate: IconType;
export declare const GrNetworkDrive: IconType;
export declare const GrNetwork: IconType;
export declare const GrNewWindow: IconType;
export declare const GrNew: IconType;
export declare const GrNext: IconType;
export declare const GrNode: IconType;
export declare const GrNodes: IconType;
export declare const GrNorton: IconType;
export declare const GrNote: IconType;
export declare const GrNotes: IconType;
export declare const GrNotification: IconType;
export declare const GrNpm: IconType;
export declare const GrObjectGroup: IconType;
export declare const GrObjectUngroup: IconType;
export declare const GrOfflineStorage: IconType;
export declare const GrOnedrive: IconType;
export declare const GrOpera: IconType;
export declare const GrOptimize: IconType;
export declare const GrOracle: IconType;
export declare const GrOrderedList: IconType;
export declare const GrOrganization: IconType;
export declare const GrOverview: IconType;
export declare const GrPackage: IconType;
export declare const GrPaint: IconType;
export declare const GrPan: IconType;
export declare const GrPauseFill: IconType;
export declare const GrPause: IconType;
export declare const GrPaypal: IconType;
export declare const GrPerformance: IconType;
export declare const GrPersonalComputer: IconType;
export declare const GrPhoneFlip: IconType;
export declare const GrPhoneHorizontal: IconType;
export declare const GrPhoneVertical: IconType;
export declare const GrPhone: IconType;
export declare const GrPieChart: IconType;
export declare const GrPiedPiper: IconType;
export declare const GrPin: IconType;
export declare const GrPinterest: IconType;
export declare const GrPlan: IconType;
export declare const GrPlayFill: IconType;
export declare const GrPlay: IconType;
export declare const GrPlug: IconType;
export declare const GrPocket: IconType;
export declare const GrPowerCycle: IconType;
export declare const GrPowerForceShutdown: IconType;
export declare const GrPowerReset: IconType;
export declare const GrPowerShutdown: IconType;
export declare const GrPower: IconType;
export declare const GrPrevious: IconType;
export declare const GrPrint: IconType;
export declare const GrProductHunt: IconType;
export declare const GrProjects: IconType;
export declare const GrQr: IconType;
export declare const GrRadialSelected: IconType;
export declare const GrRadial: IconType;
export declare const GrRaspberry: IconType;
export declare const GrReactjs: IconType;
export declare const GrReddit: IconType;
export declare const GrRedhat: IconType;
export declare const GrRedo: IconType;
export declare const GrRefresh: IconType;
export declare const GrResources: IconType;
export declare const GrRestaurant: IconType;
export declare const GrRestroomMen: IconType;
export declare const GrRestroomWomen: IconType;
export declare const GrRestroom: IconType;
export declare const GrResume: IconType;
export declare const GrReturn: IconType;
export declare const GrRevert: IconType;
export declare const GrRewind: IconType;
export declare const GrRisk: IconType;
export declare const GrRobot: IconType;
export declare const GrRotateLeft: IconType;
export declare const GrRotateRight: IconType;
export declare const GrRss: IconType;
export declare const GrRun: IconType;
export declare const GrSafariOption: IconType;
export declare const GrSamsungPay: IconType;
export declare const GrSans: IconType;
export declare const GrSatellite: IconType;
export declare const GrSave: IconType;
export declare const GrScan: IconType;
export declare const GrScheduleNew: IconType;
export declare const GrSchedulePlay: IconType;
export declare const GrSchedule: IconType;
export declare const GrSchedules: IconType;
export declare const GrSco: IconType;
export declare const GrScorecard: IconType;
export declare const GrScript: IconType;
export declare const GrSd: IconType;
export declare const GrSearchAdvanced: IconType;
export declare const GrSearch: IconType;
export declare const GrSecure: IconType;
export declare const GrSelect: IconType;
export declare const GrSelection: IconType;
export declare const GrSemantics: IconType;
export declare const GrSend: IconType;
export declare const GrServerCluster: IconType;
export declare const GrServer: IconType;
export declare const GrServers: IconType;
export declare const GrServicePlay: IconType;
export declare const GrServices: IconType;
export declare const GrSettingsOption: IconType;
export declare const GrShareOption: IconType;
export declare const GrShareRounded: IconType;
export declare const GrShare: IconType;
export declare const GrShieldSecurity: IconType;
export declare const GrShield: IconType;
export declare const GrShift: IconType;
export declare const GrShop: IconType;
export declare const GrSidebar: IconType;
export declare const GrSign: IconType;
export declare const GrSkype: IconType;
export declare const GrSlack: IconType;
export declare const GrSnapchat: IconType;
export declare const GrSolaris: IconType;
export declare const GrSort: IconType;
export declare const GrSoundcloud: IconType;
export declare const GrSpa: IconType;
export declare const GrSpectrum: IconType;
export declare const GrSplit: IconType;
export declare const GrSplits: IconType;
export declare const GrSpotify: IconType;
export declare const GrSquare: IconType;
export declare const GrStackOverflow: IconType;
export declare const GrStakeholder: IconType;
export declare const GrStarHalf: IconType;
export declare const GrStarOutline: IconType;
export declare const GrStar: IconType;
export declare const GrStatusCriticalSmall: IconType;
export declare const GrStatusCritical: IconType;
export declare const GrStatusDisabledSmall: IconType;
export declare const GrStatusDisabled: IconType;
export declare const GrStatusGoodSmall: IconType;
export declare const GrStatusGood: IconType;
export declare const GrStatusInfoSmall: IconType;
export declare const GrStatusInfo: IconType;
export declare const GrStatusPlaceholderSmall: IconType;
export declare const GrStatusPlaceholder: IconType;
export declare const GrStatusUnknownSmall: IconType;
export declare const GrStatusUnknown: IconType;
export declare const GrStatusWarningSmall: IconType;
export declare const GrStatusWarning: IconType;
export declare const GrStepsOption: IconType;
export declare const GrSteps: IconType;
export declare const GrStopFill: IconType;
export declare const GrStop: IconType;
export declare const GrStorage: IconType;
export declare const GrStreetView: IconType;
export declare const GrStrikeThrough: IconType;
export declare const GrStripe: IconType;
export declare const GrSubscript: IconType;
export declare const GrSubtractCircle: IconType;
export declare const GrSubtract: IconType;
export declare const GrSun: IconType;
export declare const GrSuperscript: IconType;
export declare const GrSupport: IconType;
export declare const GrSuse: IconType;
export declare const GrSwift: IconType;
export declare const GrSwim: IconType;
export declare const GrSwitch: IconType;
export declare const GrSync: IconType;
export declare const GrSystem: IconType;
export declare const GrTableAdd: IconType;
export declare const GrTable: IconType;
export declare const GrTag: IconType;
export declare const GrTapeOption: IconType;
export declare const GrTape: IconType;
export declare const GrTarget: IconType;
export declare const GrTask: IconType;
export declare const GrTasks: IconType;
export declare const GrTechnology: IconType;
export declare const GrTemplate: IconType;
export declare const GrTerminal: IconType;
export declare const GrTestDesktop: IconType;
export declare const GrTest: IconType;
export declare const GrTextAlignCenter: IconType;
export declare const GrTextAlignFull: IconType;
export declare const GrTextAlignLeft: IconType;
export declare const GrTextAlignRight: IconType;
export declare const GrTextWrap: IconType;
export declare const GrThreads: IconType;
export declare const GrThreats: IconType;
export declare const GrThreeDEffects: IconType;
export declare const GrThreeD: IconType;
export declare const GrTicket: IconType;
export declare const GrTiktok: IconType;
export declare const GrTime: IconType;
export declare const GrTip: IconType;
export declare const GrToast: IconType;
export declare const GrTools: IconType;
export declare const GrTooltip: IconType;
export declare const GrTopCorner: IconType;
export declare const GrTrain: IconType;
export declare const GrTransaction: IconType;
export declare const GrTrash: IconType;
export declare const GrTreeOption: IconType;
export declare const GrTree: IconType;
export declare const GrTrigger: IconType;
export declare const GrTrophy: IconType;
export declare const GrTroubleshoot: IconType;
export declare const GrTty: IconType;
export declare const GrTumblr: IconType;
export declare const GrTurbolinux: IconType;
export declare const GrTwitch: IconType;
export declare const GrTwitter: IconType;
export declare const GrUbuntu: IconType;
export declare const GrUnderline: IconType;
export declare const GrUndo: IconType;
export declare const GrUnlink: IconType;
export declare const GrUnlock: IconType;
export declare const GrUnorderedList: IconType;
export declare const GrUnsorted: IconType;
export declare const GrUp: IconType;
export declare const GrUpdate: IconType;
export declare const GrUpgrade: IconType;
export declare const GrUploadOption: IconType;
export declare const GrUpload: IconType;
export declare const GrUsbKey: IconType;
export declare const GrUserAdd: IconType;
export declare const GrUserAdmin: IconType;
export declare const GrUserExpert: IconType;
export declare const GrUserFemale: IconType;
export declare const GrUserManager: IconType;
export declare const GrUserNew: IconType;
export declare const GrUserPolice: IconType;
export declare const GrUserSettings: IconType;
export declare const GrUserWorker: IconType;
export declare const GrUser: IconType;
export declare const GrValidate: IconType;
export declare const GrVend: IconType;
export declare const GrVenmo: IconType;
export declare const GrVideo: IconType;
export declare const GrView: IconType;
export declare const GrVimeo: IconType;
export declare const GrVirtualMachine: IconType;
export declare const GrVirtualStorage: IconType;
export declare const GrVisa: IconType;
export declare const GrVmMaintenance: IconType;
export declare const GrVmware: IconType;
export declare const GrVolumeControl: IconType;
export declare const GrVolumeLow: IconType;
export declare const GrVolumeMute: IconType;
export declare const GrVolume: IconType;
export declare const GrVulnerability: IconType;
export declare const GrWaypoint: IconType;
export declare const GrWebcam: IconType;
export declare const GrWechat: IconType;
export declare const GrWhatsapp: IconType;
export declare const GrWheelchairActive: IconType;
export declare const GrWheelchair: IconType;
export declare const GrWifiLow: IconType;
export declare const GrWifiMedium: IconType;
export declare const GrWifiNone: IconType;
export declare const GrWifi: IconType;
export declare const GrWindowsLegacy: IconType;
export declare const GrWindows: IconType;
export declare const GrWordpress: IconType;
export declare const GrWorkshop: IconType;
export declare const GrX: IconType;
export declare const GrXing: IconType;
export declare const GrYoga: IconType;
export declare const GrYoutube: IconType;
export declare const GrZoomIn: IconType;
export declare const GrZoomOut: IconType;
export declare const GrZoom: IconType;
