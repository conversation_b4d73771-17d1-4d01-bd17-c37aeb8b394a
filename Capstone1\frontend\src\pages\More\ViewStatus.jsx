import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavBar from '../../components/NavBar';
import '../../styles/custom-colors.css';
import '../../styles/PageTable.css';
import '../../styles/GlobalTableStyles.css';
import '../../styles/Categories.css';
import '../../styles/Assets.css';
import '../../styles/CategoryQuantityFix.css';
import DeleteModal from '../../components/Modals/DeleteModal';
import MediumButtons from "../../components/buttons/MediumButtons";
import TableBtn from "../../components/buttons/TableButtons";

export default function ViewStatus() {
  const navigate = useNavigate();
  const [statuses, setStatuses] = useState([
    {
      id: 1,
      name: "Deployable",
      type: "Asset",
      notes: "Ready to be deployed to users",
      count: 15
    },
    {
      id: 2,
      name: "Deployed",
      type: "Asset", 
      notes: "Currently in use by a user",
      count: 8
    },
    {
      id: 3,
      name: "Pending",
      type: "Asset",
      notes: "Awaiting approval or processing",
      count: 3
    },
    {
      id: 4,
      name: "Archived",
      type: "Asset",
      notes: "No longer in active use",
      count: 2
    },
    {
      id: 5,
      name: "Undeployable",
      type: "Asset",
      notes: "Cannot be deployed due to issues",
      count: 1
    }
  ]);

  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [statusToDelete, setStatusToDelete] = useState(null);

  useEffect(() => {
    console.log("ViewStatus component mounted with navigate:", navigate);
  }, [navigate]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Navigate to edit page
  const handleEditStatus = (statusId) => {
    console.log(`/More/StatusEdit/${statusId}`);
    navigate(`/More/StatusEdit/${statusId}`);
  };

  // Show delete modal
  const handleDeleteClick = (statusId) => {
    console.log(`Opening delete modal for status ${statusId}`);
    setStatusToDelete(statusId);
    setShowDeleteModal(true);
  };

  // Handle actual deletion
  const confirmDelete = () => {
    if (statusToDelete) {
      setStatuses(statuses.filter(status => status.id !== statusToDelete));
      setShowDeleteModal(false);
      setStatusToDelete(null);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setStatusToDelete(null);
  };

  // Filter statuses based on search query
  const filteredStatuses = statuses.filter(status =>
    status.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <nav>
        <NavBar />
      </nav>
      <main className="page">
        <div className="container">
          <section className="top">
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', margin: '0', color: '#545f71' }}>Statuses ({statuses.length})</h1>
            <div>
              <form action="" method="post" style={{ marginRight: '10px' }}>
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="search-input"
                />
              </form>
              <MediumButtons type="export" />
              <MediumButtons type="new" navigatePage="/More/StatusRegistration" />
            </div>
          </section>
          <section className="middle">
            <table className="assets-table" style={{ borderRadius: '0', overflow: 'hidden' }}>
              <thead>
                <tr>
                  <th style={{ width: '40px' }}>
                    <input type="checkbox" />
                  </th>
                  <th style={{ width: '30%' }}>NAME</th>
                  <th style={{ width: '20%' }}>TYPE</th>
                  <th style={{ width: '30%' }}>NOTES</th>
                  <th style={{ width: '10%' }}>COUNT</th>
                  <th style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>EDIT</th>
                  <th style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>DELETE</th>
                </tr>
              </thead>
              <tbody>
                {filteredStatuses.map((status) => (
                  <tr key={status.id}>
                    <td style={{ width: '40px' }}>
                      <input type="checkbox" />
                    </td>
                    <td style={{ width: '30%' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <div style={{ 
                          display: 'inline-block', 
                          width: '12px', 
                          height: '12px', 
                          backgroundColor: status.name === 'Deployable' ? '#28a745' : 
                                         status.name === 'Deployed' ? '#007bff' : 
                                         status.name === 'Pending' ? '#ffc107' : 
                                         status.name === 'Archived' ? '#6c757d' : '#dc3545', 
                          borderRadius: '50%'
                        }}></div>
                        <span style={{ color: '#545f71' }}>{status.name}</span>
                      </div>
                    </td>
                    <td style={{ width: '20%', color: '#545f71' }}>{status.type}</td>
                    <td style={{ width: '30%', color: '#545f71' }}>{status.notes}</td>
                    <td style={{ width: '10%', color: '#545f71' }}>{status.count}</td>
                    <td style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>
                      <TableBtn
                        type="edit"
                        navigatePage={`/More/StatusEdit/${status.id}`}
                      />
                    </td>
                    <td style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>
                      <TableBtn
                        type="delete"
                        showModal={() => handleDeleteClick(status.id)}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </section>
          <section className="bottom" style={{ width: '100%', display: 'flex', justifyContent: 'space-between', padding: '16px 34px', borderTop: '1px solid #d3d3d3' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#545f71' }}>
              <span style={{ color: '#545f71' }}>Show</span>
              <select value={itemsPerPage} onChange={(e) => setItemsPerPage(Number(e.target.value))} style={{ color: '#545f71' }}>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span style={{ color: '#545f71' }}>items per page</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <button className="prev-btn" disabled={currentPage === 1} style={{ color: '#545f71', border: '1px solid #dee2e6', background: 'white', padding: '4px 8px', borderRadius: '4px' }}>Prev</button>
              <span className="page-number" style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: '30px', height: '30px', backgroundColor: '#007bff', color: 'white', borderRadius: '4px', fontSize: '14px' }}>{currentPage}</span>
              <button className="next-btn" disabled={filteredStatuses.length <= itemsPerPage} style={{ color: '#545f71', border: '1px solid #dee2e6', background: 'white', padding: '4px 8px', borderRadius: '4px' }}>Next</button>
            </div>
          </section>

          {/* Delete Modal */}
          {showDeleteModal && (
            <DeleteModal
              isOpen={showDeleteModal}
              onConfirm={confirmDelete}
              onCancel={cancelDelete}
              title="Delete Status"
              message="Are you sure you want to delete this status? This action cannot be undone."
            />
          )}
        </div>
      </main>
    </>
  );
}
