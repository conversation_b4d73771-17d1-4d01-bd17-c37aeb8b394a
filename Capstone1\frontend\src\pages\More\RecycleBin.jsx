import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavBar from '../../components/NavBar';
import '../../styles/custom-colors.css';
import '../../styles/PageTable.css';
import '../../styles/GlobalTableStyles.css';
import '../../styles/ViewManufacturer.css';
import '../../styles/TableButtons.css';
import MediumButtons from "../../components/buttons/MediumButtons";
import TableBtn from "../../components/buttons/TableButtons";

export default function RecycleBin() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('assets');
  const [searchQuery, setSearchQuery] = useState('');
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample deleted data for different categories
  const deletedData = {
    assets: [],
    accessories: [],
    components: [],
    consumables: [
      {
        id: 1,
        name: 'Printer Paper A4',
        category: 'Office Supplies',
        deletedDate: '2024-01-15',
        deletedBy: 'Admin User'
      },
      {
        id: 2,
        name: 'Ink Cartridge HP',
        category: 'Printer Supplies',
        deletedDate: '2024-01-10',
        deletedBy: 'Admin User'
      },
      {
        id: 3,
        name: 'USB Cable Type-C',
        category: 'Cables',
        deletedDate: '2024-01-08',
        deletedBy: 'Admin User'
      }
    ]
  };

  const tabs = [
    { key: 'assets', label: 'Assets', count: deletedData.assets.length },
    { key: 'accessories', label: 'Accessories', count: deletedData.accessories.length },
    { key: 'components', label: 'Components', count: deletedData.components.length },
    { key: 'consumables', label: 'Consumables', count: deletedData.consumables.length }
  ];

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Filter data based on search query
  const filteredData = deletedData[activeTab].filter(item =>
    item.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle restore action
  const handleRestore = (itemId) => {
    console.log(`Restoring ${activeTab} item with ID: ${itemId}`);
    // Here you would typically call an API to restore the item
  };

  // Handle permanent delete action
  const handlePermanentDelete = (itemId) => {
    console.log(`Permanently deleting ${activeTab} item with ID: ${itemId}`);
    // Here you would typically call an API to permanently delete the item
  };

  const renderTabContent = () => {
    if (filteredData.length === 0) {
      return (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px',
          color: '#6c757d',
          fontSize: '1.1rem'
        }}>
          No {tabs.find(tab => tab.key === activeTab)?.label} Found
        </div>
      );
    }

    return (
      <table className="assets-table" style={{ borderRadius: '0', overflow: 'hidden' }}>
        <thead>
          <tr>
            <th style={{ width: '40px' }}>
              <input type="checkbox" />
            </th>
            <th style={{ width: '30%' }}>NAME</th>
            <th style={{ width: '25%' }}>CATEGORY</th>
            <th style={{ width: '20%' }}>DELETED DATE</th>
            <th style={{ width: '15%' }}>DELETED BY</th>
            <th style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>RESTORE</th>
            <th style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>DELETE</th>
          </tr>
        </thead>
        <tbody>
          {filteredData.map((item) => (
            <tr key={item.id}>
              <td style={{ width: '40px' }}>
                <input type="checkbox" />
              </td>
              <td style={{ width: '30%', color: '#545f71' }}>{item.name}</td>
              <td style={{ width: '25%', color: '#545f71' }}>{item.category}</td>
              <td style={{ width: '20%', color: '#545f71' }}>{item.deletedDate}</td>
              <td style={{ width: '15%', color: '#545f71' }}>{item.deletedBy}</td>
              <td style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>
                <button
                  onClick={() => handleRestore(item.id)}
                  style={{
                    background: '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    padding: '6px 12px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Restore
                </button>
              </td>
              <td style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>
                <TableBtn
                  type="delete"
                  showModal={() => handlePermanentDelete(item.id)}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  return (
    <>
      <nav>
        <NavBar />
      </nav>
      <main className="page">
        <div className="container">
          <section className="top">
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', margin: '0', color: '#545f71' }}>
              Recycle Bin
            </h1>
          </section>

          {/* Tabs */}
          <section className="tabs" style={{ marginBottom: '20px' }}>
            <div style={{ 
              display: 'flex', 
              borderBottom: '1px solid #dee2e6',
              marginBottom: '20px'
            }}>
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  style={{
                    padding: '12px 24px',
                    border: 'none',
                    background: 'transparent',
                    color: activeTab === tab.key ? '#007bff' : '#6c757d',
                    borderBottom: activeTab === tab.key ? '2px solid #007bff' : '2px solid transparent',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: activeTab === tab.key ? '600' : '400'
                  }}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </section>

          {/* Tab Content Header */}
          <section className="tab-header" style={{ marginBottom: '20px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ 
                fontSize: '1.25rem', 
                fontWeight: '600', 
                margin: '0', 
                color: '#545f71' 
              }}>
                {tabs.find(tab => tab.key === activeTab)?.label} ({filteredData.length})
              </h2>
              <div style={{ display: 'flex', gap: '10px' }}>
                <form action="" method="post" style={{ marginRight: '10px' }}>
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="search-input"
                  />
                </form>
                <MediumButtons type="export" />
              </div>
            </div>
          </section>

          {/* Tab Content */}
          <section className="middle">
            {renderTabContent()}
          </section>

          {/* Pagination */}
          {filteredData.length > 0 && (
            <section className="bottom" style={{ 
              width: '100%', 
              display: 'flex', 
              justifyContent: 'space-between', 
              padding: '16px 34px', 
              borderTop: '1px solid #d3d3d3',
              marginTop: '20px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#545f71' }}>
                <span style={{ color: '#545f71' }}>Show</span>
                <select value={itemsPerPage} onChange={(e) => setItemsPerPage(Number(e.target.value))} style={{ color: '#545f71' }}>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span style={{ color: '#545f71' }}>items per page</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <button className="prev-btn" disabled={currentPage === 1} style={{ 
                  color: '#545f71', 
                  border: '1px solid #dee2e6', 
                  background: 'white', 
                  padding: '4px 8px', 
                  borderRadius: '4px' 
                }}>Prev</button>
                <span className="page-number" style={{ 
                  display: 'inline-flex', 
                  alignItems: 'center', 
                  justifyContent: 'center', 
                  width: '30px', 
                  height: '30px', 
                  backgroundColor: '#007bff', 
                  color: 'white', 
                  borderRadius: '4px', 
                  fontSize: '14px' 
                }}>{currentPage}</span>
                <button className="next-btn" disabled={filteredData.length <= itemsPerPage} style={{ 
                  color: '#545f71', 
                  border: '1px solid #dee2e6', 
                  background: 'white', 
                  padding: '4px 8px', 
                  borderRadius: '4px' 
                }}>Next</button>
              </div>
            </section>
          )}
        </div>
      </main>
    </>
  );
}
