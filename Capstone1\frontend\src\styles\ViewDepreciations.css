/* ViewDepreciations Component Styles */

/* Depreciation Page Header */
.depreciation-page-header {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #545f71;
}

/* Depreciation Top Section */
.depreciation-top-section {
  /* Inherits from existing styles */
}

.depreciation-top-section-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Depreciation Search Form */
.depreciation-search-form {
  margin-right: 10px;
}

.depreciation-search-input {
  /* Inherits from search-input class */
}

/* Depreciation Table Container */
.depreciation-table-container {
  border-radius: 0;
  overflow: hidden;
}

/* Depreciation Table Headers */
.depreciation-table-header-checkbox {
  width: 40px;
}

.depreciation-table-header-name {
  width: 40%;
}

.depreciation-table-header-duration {
  width: 25%;
}

.depreciation-table-header-minimum-value {
  width: 25%;
}

.depreciation-table-header-action {
  width: 40px;
  text-align: center;
  padding-left: 12px;
  padding-right: 12px;
}

/* Depreciation Table Cells */
.depreciation-table-cell-checkbox {
  width: 40px;
}

.depreciation-table-cell-name {
  width: 40%;
  color: #545f71;
}

.depreciation-table-cell-duration {
  width: 25%;
  color: #545f71;
}

.depreciation-table-cell-minimum-value {
  width: 25%;
  color: #545f71;
}

.depreciation-table-cell-action {
  width: 40px;
  text-align: center;
  padding-left: 12px;
  padding-right: 12px;
}

/* Depreciation Pagination */
.depreciation-pagination-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  margin-top: 0;
}

.depreciation-pagination-left {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 14px;
}

.depreciation-pagination-text {
  color: #6c757d;
  font-size: 14px;
}

.depreciation-pagination-select {
  color: #6c757d;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  background-color: white;
}

.depreciation-pagination-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.depreciation-prev-btn {
  color: #6c757d;
  border: 1px solid #ced4da;
  background: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.depreciation-prev-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.depreciation-prev-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.depreciation-next-btn {
  color: #6c757d;
  border: 1px solid #ced4da;
  background: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.depreciation-next-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.depreciation-next-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.depreciation-page-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

/* Depreciation Table Button Alignment */
.depreciation-table tbody tr {
  height: 60px;
}

.depreciation-table tbody td {
  vertical-align: middle;
}

.depreciation-table .table-buttons-edit,
.depreciation-table .table-buttons-delete {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
