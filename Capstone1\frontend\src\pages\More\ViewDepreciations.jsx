import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavBar from '../../components/NavBar';
import '../../styles/custom-colors.css';
import '../../styles/PageTable.css';
import '../../styles/GlobalTableStyles.css';
import '../../styles/ViewManufacturer.css';
import '../../styles/TableButtons.css';
import DeleteModal from '../../components/Modals/DeleteModal';
import MediumButtons from "../../components/buttons/MediumButtons";
import TableBtn from "../../components/buttons/TableButtons";

export default function ViewDepreciations() {
  const navigate = useNavigate();
  const [depreciations, setDepreciations] = useState([
    {
      id: 1,
      name: "iPhone Depreciation",
      duration: "24 months",
      minimumValue: "PHP 2,000"
    },
    {
      id: 2,
      name: "Laptop Depreciation",
      duration: "36 months",
      minimumValue: "PHP 5,000"
    }
  ]);

  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [depreciationToDelete, setDepreciationToDelete] = useState(null);

  useEffect(() => {
    console.log("ViewDepreciations component mounted with navigate:", navigate);
  }, [navigate]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Navigate to edit page
  const handleEditDepreciation = (depreciationId) => {
    console.log(`/More/DepreciationEdit/${depreciationId}`);
    navigate(`/More/DepreciationEdit/${depreciationId}`);
  };

  // Show delete modal
  const handleDeleteClick = (depreciationId) => {
    console.log(`Opening delete modal for depreciation ${depreciationId}`);
    setDepreciationToDelete(depreciationId);
    setShowDeleteModal(true);
  };

  // Handle actual deletion
  const confirmDelete = () => {
    if (depreciationToDelete) {
      setDepreciations(depreciations.filter(depreciation => depreciation.id !== depreciationToDelete));
      setShowDeleteModal(false);
      setDepreciationToDelete(null);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDepreciationToDelete(null);
  };

  // Filter depreciations based on search query
  const filteredDepreciations = depreciations.filter(depreciation =>
    depreciation.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <nav>
        <NavBar />
      </nav>
      <main className="page">
        <div className="container">
          <section className="top">
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', margin: '0', color: '#545f71' }}>
              Depreciation's ({depreciations.length})
            </h1>
            <div>
              <form action="" method="post" style={{ marginRight: '10px' }}>
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="search-input"
                />
              </form>
              <MediumButtons type="export" />
              <MediumButtons type="new" navigatePage="/More/DepreciationRegistration" />
            </div>
          </section>
          <section className="middle">
            <table className="assets-table" style={{ borderRadius: '0', overflow: 'hidden' }}>
              <thead>
                <tr>
                  <th style={{ width: '40px' }}>
                    <input type="checkbox" />
                  </th>
                  <th style={{ width: '40%' }}>NAME</th>
                  <th style={{ width: '25%' }}>DURATION</th>
                  <th style={{ width: '25%' }}>MINIMUM VALUE</th>
                  <th style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>EDIT</th>
                  <th style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>DELETE</th>
                </tr>
              </thead>
              <tbody>
                {filteredDepreciations.map((depreciation) => (
                  <tr key={depreciation.id}>
                    <td style={{ width: '40px' }}>
                      <input type="checkbox" />
                    </td>
                    <td style={{ width: '40%', color: '#545f71' }}>{depreciation.name}</td>
                    <td style={{ width: '25%', color: '#545f71' }}>{depreciation.duration}</td>
                    <td style={{ width: '25%', color: '#545f71' }}>{depreciation.minimumValue}</td>
                    <td style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>
                      <TableBtn
                        type="edit"
                        navigatePage={`/More/DepreciationEdit/${depreciation.id}`}
                      />
                    </td>
                    <td style={{ width: '40px', textAlign: 'center', paddingLeft: '12px', paddingRight: '12px' }}>
                      <TableBtn
                        type="delete"
                        showModal={() => handleDeleteClick(depreciation.id)}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </section>
          <section className="bottom" style={{ 
            width: '100%', 
            display: 'flex', 
            justifyContent: 'space-between', 
            padding: '16px 34px', 
            borderTop: '1px solid #d3d3d3' 
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#545f71' }}>
              <span style={{ color: '#545f71' }}>Show</span>
              <select value={itemsPerPage} onChange={(e) => setItemsPerPage(Number(e.target.value))} style={{ color: '#545f71' }}>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span style={{ color: '#545f71' }}>items per page</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <button className="prev-btn" disabled={currentPage === 1} style={{ 
                color: '#545f71', 
                border: '1px solid #dee2e6', 
                background: 'white', 
                padding: '4px 8px', 
                borderRadius: '4px' 
              }}>Prev</button>
              <span className="page-number" style={{ 
                display: 'inline-flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                width: '30px', 
                height: '30px', 
                backgroundColor: '#007bff', 
                color: 'white', 
                borderRadius: '4px', 
                fontSize: '14px' 
              }}>{currentPage}</span>
              <button className="next-btn" disabled={filteredDepreciations.length <= itemsPerPage} style={{ 
                color: '#545f71', 
                border: '1px solid #dee2e6', 
                background: 'white', 
                padding: '4px 8px', 
                borderRadius: '4px' 
              }}>Next</button>
            </div>
          </section>

          {/* Delete Modal */}
          {showDeleteModal && (
            <DeleteModal
              isOpen={showDeleteModal}
              onConfirm={confirmDelete}
              onCancel={cancelDelete}
              title="Delete Depreciation"
              message="Are you sure you want to delete this depreciation? This action cannot be undone."
            />
          )}
        </div>
      </main>
    </>
  );
}
