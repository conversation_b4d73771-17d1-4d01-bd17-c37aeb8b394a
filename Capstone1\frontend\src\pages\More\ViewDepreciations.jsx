import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavBar from '../../components/NavBar';
import '../../styles/custom-colors.css';
import '../../styles/PageTable.css';
import '../../styles/GlobalTableStyles.css';
import '../../styles/ViewManufacturer.css';
import '../../styles/TableButtons.css';
import '../../styles/ViewDepreciations.css';
import DeleteModal from '../../components/Modals/DeleteModal';
import MediumButtons from "../../components/buttons/MediumButtons";
import TableBtn from "../../components/buttons/TableButtons";

export default function ViewDepreciations() {
  const navigate = useNavigate();
  const [depreciations, setDepreciations] = useState([
    {
      id: 1,
      name: "iPhone Depreciation",
      duration: "24 months",
      minimumValue: "PHP 2,000"
    },
    {
      id: 2,
      name: "Laptop Depreciation",
      duration: "36 months",
      minimumValue: "PHP 5,000"
    }
  ]);

  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [depreciationToDelete, setDepreciationToDelete] = useState(null);

  useEffect(() => {
    console.log("ViewDepreciations component mounted with navigate:", navigate);
  }, [navigate]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Navigate to edit page
  const handleEditDepreciation = (depreciationId) => {
    console.log(`/More/DepreciationEdit/${depreciationId}`);
    navigate(`/More/DepreciationEdit/${depreciationId}`);
  };

  // Show delete modal
  const handleDeleteClick = (depreciationId) => {
    console.log(`Opening delete modal for depreciation ${depreciationId}`);
    setDepreciationToDelete(depreciationId);
    setShowDeleteModal(true);
  };

  // Handle actual deletion
  const confirmDelete = () => {
    if (depreciationToDelete) {
      setDepreciations(depreciations.filter(depreciation => depreciation.id !== depreciationToDelete));
      setShowDeleteModal(false);
      setDepreciationToDelete(null);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDepreciationToDelete(null);
  };

  // Filter depreciations based on search query
  const filteredDepreciations = depreciations.filter(depreciation =>
    depreciation.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <nav>
        <NavBar />
      </nav>
      <main className="page">
        <div className="container">
          <section className="top depreciation-top-section">
            <h1 className="depreciation-page-header">
              Depreciation's ({depreciations.length})
            </h1>
            <div className="depreciation-top-section-actions">
              <form action="" method="post" className="depreciation-search-form">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="search-input depreciation-search-input"
                />
              </form>
              <MediumButtons type="export" />
              <MediumButtons type="new" navigatePage="/More/DepreciationRegistration" />
            </div>
          </section>
          <section className="middle">
            <table className="assets-table depreciation-table depreciation-table-container">
              <thead>
                <tr>
                  <th className="depreciation-table-header-checkbox">
                    <input type="checkbox" />
                  </th>
                  <th className="depreciation-table-header-name">NAME</th>
                  <th className="depreciation-table-header-duration">DURATION</th>
                  <th className="depreciation-table-header-minimum-value">MINIMUM VALUE</th>
                  <th className="depreciation-table-header-action">EDIT</th>
                  <th className="depreciation-table-header-action">DELETE</th>
                </tr>
              </thead>
              <tbody>
                {filteredDepreciations.map((depreciation) => (
                  <tr key={depreciation.id}>
                    <td className="depreciation-table-cell-checkbox">
                      <input type="checkbox" />
                    </td>
                    <td className="depreciation-table-cell-name">{depreciation.name}</td>
                    <td className="depreciation-table-cell-duration">{depreciation.duration}</td>
                    <td className="depreciation-table-cell-minimum-value">{depreciation.minimumValue}</td>
                    <td className="depreciation-table-cell-action">
                      <TableBtn
                        type="edit"
                        navigatePage={`/More/DepreciationEdit/${depreciation.id}`}
                      />
                    </td>
                    <td className="depreciation-table-cell-action">
                      <TableBtn
                        type="delete"
                        showModal={() => handleDeleteClick(depreciation.id)}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </section>
          <section className="bottom depreciation-pagination-container">
            <div className="depreciation-pagination-left">
              <span className="depreciation-pagination-text">Show</span>
              <select value={itemsPerPage} onChange={(e) => setItemsPerPage(Number(e.target.value))} className="depreciation-pagination-select">
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span className="depreciation-pagination-text">items per page</span>
            </div>
            <div className="depreciation-pagination-right">
              <button className="prev-btn depreciation-prev-btn" disabled={currentPage === 1}>Prev</button>
              <span className="page-number depreciation-page-number">{currentPage}</span>
              <button className="next-btn depreciation-next-btn" disabled={filteredDepreciations.length <= itemsPerPage}>Next</button>
            </div>
          </section>

          {/* Delete Modal */}
          {showDeleteModal && (
            <DeleteModal
              isOpen={showDeleteModal}
              onConfirm={confirmDelete}
              onCancel={cancelDelete}
              title="Delete Depreciation"
              message="Are you sure you want to delete this depreciation? This action cannot be undone."
            />
          )}
        </div>
      </main>
    </>
  );
}
