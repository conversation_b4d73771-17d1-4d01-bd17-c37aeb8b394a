/* View Modal Styles */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.view-modal {
  min-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #495057;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.close-button svg {
  width: 20px;
  height: 20px;
}

.modal-body {
  padding: 24px;
}

.view-content {
  display: flex;
  gap: 24px;
}

.image-section {
  flex-shrink: 0;
  width: 200px;
}

.consumable-image,
.accessory-image,
.component-image,
.asset-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-group {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 16px;
  background-color: #f8f9fa;
}

.detail-group h3 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-weight: 500;
  color: #6c757d;
  min-width: 140px;
  flex-shrink: 0;
}

.detail-row .value {
  color: #495057;
  text-align: right;
  flex: 1;
  word-break: break-word;
}

.detail-row .value.notes {
  text-align: left;
  margin-top: 4px;
  line-height: 1.5;
  background-color: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background-color: #0056b3;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.available {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.checked-out {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.maintenance {
  background-color: #f8d7da;
  color: #721c24;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
    max-height: calc(100vh - 40px);
  }
  
  .view-modal {
    min-width: unset;
  }
  
  .view-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .image-section {
    width: 100%;
    align-self: center;
  }
  
  .consumable-image,
  .accessory-image,
  .component-image,
  .asset-image {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    display: block;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-row .label {
    min-width: unset;
  }
  
  .detail-row .value {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 16px;
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .modal-footer {
    padding: 12px 16px;
  }
  
  .detail-group {
    padding: 12px;
  }
}
