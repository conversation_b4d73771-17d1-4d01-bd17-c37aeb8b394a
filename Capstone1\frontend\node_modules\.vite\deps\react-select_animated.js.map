{"version": 3, "sources": ["../../react-select/animated/dist/react-select-animated.esm.js"], "sourcesContent": ["import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport memoizeOne from 'memoize-one';\nimport { F as defaultComponents } from '../../dist/index-641ee5b8.esm.js';\nimport * as React from 'react';\nimport { useRef, useState, useEffect } from 'react';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport { Transition, TransitionGroup } from 'react-transition-group';\nimport '@emotion/react';\nimport '@babel/runtime/helpers/typeof';\nimport '@babel/runtime/helpers/taggedTemplateLiteral';\nimport '@babel/runtime/helpers/defineProperty';\nimport 'react-dom';\nimport '@floating-ui/dom';\nimport 'use-isomorphic-layout-effect';\n\nvar _excluded$4 = [\"in\", \"onExited\", \"appear\", \"enter\", \"exit\"];\n// strip transition props off before spreading onto select component\nvar AnimatedInput = function AnimatedInput(WrappedComponent) {\n  return function (_ref) {\n    _ref.in;\n      _ref.onExited;\n      _ref.appear;\n      _ref.enter;\n      _ref.exit;\n      var props = _objectWithoutProperties(_ref, _excluded$4);\n    return /*#__PURE__*/React.createElement(WrappedComponent, props);\n  };\n};\nvar AnimatedInput$1 = AnimatedInput;\n\nvar _excluded$3 = [\"component\", \"duration\", \"in\", \"onExited\"];\nvar Fade = function Fade(_ref) {\n  var Tag = _ref.component,\n    _ref$duration = _ref.duration,\n    duration = _ref$duration === void 0 ? 1 : _ref$duration,\n    inProp = _ref.in;\n    _ref.onExited;\n    var props = _objectWithoutProperties(_ref, _excluded$3);\n  var nodeRef = useRef(null);\n  var transition = {\n    entering: {\n      opacity: 0\n    },\n    entered: {\n      opacity: 1,\n      transition: \"opacity \".concat(duration, \"ms\")\n    },\n    exiting: {\n      opacity: 0\n    },\n    exited: {\n      opacity: 0\n    }\n  };\n  return /*#__PURE__*/React.createElement(Transition, {\n    mountOnEnter: true,\n    unmountOnExit: true,\n    in: inProp,\n    timeout: duration,\n    nodeRef: nodeRef\n  }, function (state) {\n    var innerProps = {\n      style: _objectSpread({}, transition[state]),\n      ref: nodeRef\n    };\n    return /*#__PURE__*/React.createElement(Tag, _extends({\n      innerProps: innerProps\n    }, props));\n  });\n};\n\n// ==============================\n// Collapse Transition\n// ==============================\n\nvar collapseDuration = 260;\n// wrap each MultiValue with a collapse transition; decreases width until\n// finally removing from DOM\nvar Collapse = function Collapse(_ref2) {\n  var children = _ref2.children,\n    _in = _ref2.in,\n    _onExited = _ref2.onExited;\n  var ref = useRef(null);\n  var _useState = useState('auto'),\n    _useState2 = _slicedToArray(_useState, 2),\n    width = _useState2[0],\n    setWidth = _useState2[1];\n  useEffect(function () {\n    var el = ref.current;\n    if (!el) return;\n\n    /*\n      Here we're invoking requestAnimationFrame with a callback invoking our\n      call to getBoundingClientRect and setState in order to resolve an edge case\n      around portalling. Certain portalling solutions briefly remove children from the DOM\n      before appending them to the target node. This is to avoid us trying to call getBoundingClientrect\n      while the Select component is in this state.\n    */\n    // cannot use `offsetWidth` because it is rounded\n    var rafId = window.requestAnimationFrame(function () {\n      return setWidth(el.getBoundingClientRect().width);\n    });\n    return function () {\n      return window.cancelAnimationFrame(rafId);\n    };\n  }, []);\n  var getStyleFromStatus = function getStyleFromStatus(status) {\n    switch (status) {\n      default:\n        return {\n          width: width\n        };\n      case 'exiting':\n        return {\n          width: 0,\n          transition: \"width \".concat(collapseDuration, \"ms ease-out\")\n        };\n      case 'exited':\n        return {\n          width: 0\n        };\n    }\n  };\n  return /*#__PURE__*/React.createElement(Transition, {\n    enter: false,\n    mountOnEnter: true,\n    unmountOnExit: true,\n    in: _in,\n    onExited: function onExited() {\n      var el = ref.current;\n      if (!el) return;\n      _onExited === null || _onExited === void 0 ? void 0 : _onExited(el);\n    },\n    timeout: collapseDuration,\n    nodeRef: ref\n  }, function (status) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: ref,\n      style: _objectSpread({\n        overflow: 'hidden',\n        whiteSpace: 'nowrap'\n      }, getStyleFromStatus(status))\n    }, children);\n  });\n};\n\nvar _excluded$2 = [\"in\", \"onExited\"];\n// strip transition props off before spreading onto actual component\n\nvar AnimatedMultiValue = function AnimatedMultiValue(WrappedComponent) {\n  return function (_ref) {\n    var inProp = _ref.in,\n      onExited = _ref.onExited,\n      props = _objectWithoutProperties(_ref, _excluded$2);\n    return /*#__PURE__*/React.createElement(Collapse, {\n      in: inProp,\n      onExited: onExited\n    }, /*#__PURE__*/React.createElement(WrappedComponent, _extends({\n      cropWithEllipsis: inProp\n    }, props)));\n  };\n};\nvar AnimatedMultiValue$1 = AnimatedMultiValue;\n\n// fade in when last multi-value removed, otherwise instant\nvar AnimatedPlaceholder = function AnimatedPlaceholder(WrappedComponent) {\n  return function (props) {\n    return /*#__PURE__*/React.createElement(Fade, _extends({\n      component: WrappedComponent,\n      duration: props.isMulti ? collapseDuration : 1\n    }, props));\n  };\n};\nvar AnimatedPlaceholder$1 = AnimatedPlaceholder;\n\n// instant fade; all transition-group children must be transitions\n\nvar AnimatedSingleValue = function AnimatedSingleValue(WrappedComponent) {\n  return function (props) {\n    return /*#__PURE__*/React.createElement(Fade, _extends({\n      component: WrappedComponent\n    }, props));\n  };\n};\nvar AnimatedSingleValue$1 = AnimatedSingleValue;\n\nvar _excluded$1 = [\"component\"],\n  _excluded2 = [\"children\"];\n// make ValueContainer a transition group\nvar AnimatedValueContainer = function AnimatedValueContainer(WrappedComponent) {\n  return function (props) {\n    return props.isMulti ? /*#__PURE__*/React.createElement(IsMultiValueContainer, _extends({\n      component: WrappedComponent\n    }, props)) : /*#__PURE__*/React.createElement(TransitionGroup, _extends({\n      component: WrappedComponent\n    }, props));\n  };\n};\nvar IsMultiValueContainer = function IsMultiValueContainer(_ref) {\n  var component = _ref.component,\n    restProps = _objectWithoutProperties(_ref, _excluded$1);\n  var multiProps = useIsMultiValueContainer(restProps);\n  return /*#__PURE__*/React.createElement(TransitionGroup, _extends({\n    component: component\n  }, multiProps));\n};\nvar useIsMultiValueContainer = function useIsMultiValueContainer(_ref2) {\n  var children = _ref2.children,\n    props = _objectWithoutProperties(_ref2, _excluded2);\n  var isMulti = props.isMulti,\n    hasValue = props.hasValue,\n    innerProps = props.innerProps,\n    _props$selectProps = props.selectProps,\n    components = _props$selectProps.components,\n    controlShouldRenderValue = _props$selectProps.controlShouldRenderValue;\n  var _useState = useState(isMulti && controlShouldRenderValue && hasValue),\n    _useState2 = _slicedToArray(_useState, 2),\n    cssDisplayFlex = _useState2[0],\n    setCssDisplayFlex = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    removingValue = _useState4[0],\n    setRemovingValue = _useState4[1];\n  useEffect(function () {\n    if (hasValue && !cssDisplayFlex) {\n      setCssDisplayFlex(true);\n    }\n  }, [hasValue, cssDisplayFlex]);\n  useEffect(function () {\n    if (removingValue && !hasValue && cssDisplayFlex) {\n      setCssDisplayFlex(false);\n    }\n    setRemovingValue(false);\n  }, [removingValue, hasValue, cssDisplayFlex]);\n  var onExited = function onExited() {\n    return setRemovingValue(true);\n  };\n  var childMapper = function childMapper(child) {\n    if (isMulti && /*#__PURE__*/React.isValidElement(child)) {\n      // Add onExited callback to MultiValues\n      if (child.type === components.MultiValue) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          onExited: onExited\n        });\n      }\n      // While container flexed, Input cursor is shown after Placeholder text,\n      // so remove Placeholder until display is set back to grid\n      if (child.type === components.Placeholder && cssDisplayFlex) {\n        return null;\n      }\n    }\n    return child;\n  };\n  var newInnerProps = _objectSpread(_objectSpread({}, innerProps), {}, {\n    style: _objectSpread(_objectSpread({}, innerProps === null || innerProps === void 0 ? void 0 : innerProps.style), {}, {\n      display: isMulti && hasValue || cssDisplayFlex ? 'flex' : 'grid'\n    })\n  });\n  var newProps = _objectSpread(_objectSpread({}, props), {}, {\n    innerProps: newInnerProps,\n    children: React.Children.toArray(children).map(childMapper)\n  });\n  return newProps;\n};\nvar AnimatedValueContainer$1 = AnimatedValueContainer;\n\nvar _excluded = [\"Input\", \"MultiValue\", \"Placeholder\", \"SingleValue\", \"ValueContainer\"];\nvar makeAnimated = function makeAnimated() {\n  var externalComponents = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var components = defaultComponents({\n    components: externalComponents\n  });\n  var Input = components.Input,\n    MultiValue = components.MultiValue,\n    Placeholder = components.Placeholder,\n    SingleValue = components.SingleValue,\n    ValueContainer = components.ValueContainer,\n    rest = _objectWithoutProperties(components, _excluded);\n  return _objectSpread({\n    Input: AnimatedInput$1(Input),\n    MultiValue: AnimatedMultiValue$1(MultiValue),\n    Placeholder: AnimatedPlaceholder$1(Placeholder),\n    SingleValue: AnimatedSingleValue$1(SingleValue),\n    ValueContainer: AnimatedValueContainer$1(ValueContainer)\n  }, rest);\n};\nvar AnimatedComponents = makeAnimated();\nvar Input = AnimatedComponents.Input;\nvar MultiValue = AnimatedComponents.MultiValue;\nvar Placeholder = AnimatedComponents.Placeholder;\nvar SingleValue = AnimatedComponents.SingleValue;\nvar ValueContainer = AnimatedComponents.ValueContainer;\nvar index = memoizeOne(makeAnimated);\n\nexport { Input, MultiValue, Placeholder, SingleValue, ValueContainer, index as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,YAAuB;AACvB,mBAA4C;AAQ5C,uBAAO;AAIP,IAAI,cAAc,CAAC,MAAM,YAAY,UAAU,SAAS,MAAM;AAE9D,IAAI,gBAAgB,SAASA,eAAc,kBAAkB;AAC3D,SAAO,SAAU,MAAM;AACrB,SAAK;AACH,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AACL,QAAI,QAAQ,yBAAyB,MAAM,WAAW;AACxD,WAA0B,oBAAc,kBAAkB,KAAK;AAAA,EACjE;AACF;AACA,IAAI,kBAAkB;AAEtB,IAAI,cAAc,CAAC,aAAa,YAAY,MAAM,UAAU;AAC5D,IAAI,OAAO,SAASC,MAAK,MAAM;AAC7B,MAAI,MAAM,KAAK,WACb,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,IAAI,eAC1C,SAAS,KAAK;AACd,OAAK;AACL,MAAI,QAAQ,yBAAyB,MAAM,WAAW;AACxD,MAAI,cAAU,qBAAO,IAAI;AACzB,MAAI,aAAa;AAAA,IACf,UAAU;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,YAAY,WAAW,OAAO,UAAU,IAAI;AAAA,IAC9C;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACA,SAA0B,oBAAc,oBAAY;AAAA,IAClD,cAAc;AAAA,IACd,eAAe;AAAA,IACf,IAAI;AAAA,IACJ,SAAS;AAAA,IACT;AAAA,EACF,GAAG,SAAU,OAAO;AAClB,QAAI,aAAa;AAAA,MACf,OAAO,eAAc,CAAC,GAAG,WAAW,KAAK,CAAC;AAAA,MAC1C,KAAK;AAAA,IACP;AACA,WAA0B,oBAAc,KAAK,SAAS;AAAA,MACpD;AAAA,IACF,GAAG,KAAK,CAAC;AAAA,EACX,CAAC;AACH;AAMA,IAAI,mBAAmB;AAGvB,IAAI,WAAW,SAASC,UAAS,OAAO;AACtC,MAAI,WAAW,MAAM,UACnB,MAAM,MAAM,IACZ,YAAY,MAAM;AACpB,MAAI,UAAM,qBAAO,IAAI;AACrB,MAAI,gBAAY,uBAAS,MAAM,GAC7B,aAAa,eAAe,WAAW,CAAC,GACxC,QAAQ,WAAW,CAAC,GACpB,WAAW,WAAW,CAAC;AACzB,8BAAU,WAAY;AACpB,QAAI,KAAK,IAAI;AACb,QAAI,CAAC,GAAI;AAUT,QAAI,QAAQ,OAAO,sBAAsB,WAAY;AACnD,aAAO,SAAS,GAAG,sBAAsB,EAAE,KAAK;AAAA,IAClD,CAAC;AACD,WAAO,WAAY;AACjB,aAAO,OAAO,qBAAqB,KAAK;AAAA,IAC1C;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,qBAAqB,SAASC,oBAAmB,QAAQ;AAC3D,YAAQ,QAAQ;AAAA,MACd;AACE,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,OAAO;AAAA,UACP,YAAY,SAAS,OAAO,kBAAkB,aAAa;AAAA,QAC7D;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,IACJ;AAAA,EACF;AACA,SAA0B,oBAAc,oBAAY;AAAA,IAClD,OAAO;AAAA,IACP,cAAc;AAAA,IACd,eAAe;AAAA,IACf,IAAI;AAAA,IACJ,UAAU,SAAS,WAAW;AAC5B,UAAI,KAAK,IAAI;AACb,UAAI,CAAC,GAAI;AACT,oBAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,EAAE;AAAA,IACpE;AAAA,IACA,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG,SAAU,QAAQ;AACnB,WAA0B,oBAAc,OAAO;AAAA,MAC7C;AAAA,MACA,OAAO,eAAc;AAAA,QACnB,UAAU;AAAA,QACV,YAAY;AAAA,MACd,GAAG,mBAAmB,MAAM,CAAC;AAAA,IAC/B,GAAG,QAAQ;AAAA,EACb,CAAC;AACH;AAEA,IAAI,cAAc,CAAC,MAAM,UAAU;AAGnC,IAAI,qBAAqB,SAASC,oBAAmB,kBAAkB;AACrE,SAAO,SAAU,MAAM;AACrB,QAAI,SAAS,KAAK,IAChB,WAAW,KAAK,UAChB,QAAQ,yBAAyB,MAAM,WAAW;AACpD,WAA0B,oBAAc,UAAU;AAAA,MAChD,IAAI;AAAA,MACJ;AAAA,IACF,GAAsB,oBAAc,kBAAkB,SAAS;AAAA,MAC7D,kBAAkB;AAAA,IACpB,GAAG,KAAK,CAAC,CAAC;AAAA,EACZ;AACF;AACA,IAAI,uBAAuB;AAG3B,IAAI,sBAAsB,SAASC,qBAAoB,kBAAkB;AACvE,SAAO,SAAU,OAAO;AACtB,WAA0B,oBAAc,MAAM,SAAS;AAAA,MACrD,WAAW;AAAA,MACX,UAAU,MAAM,UAAU,mBAAmB;AAAA,IAC/C,GAAG,KAAK,CAAC;AAAA,EACX;AACF;AACA,IAAI,wBAAwB;AAI5B,IAAI,sBAAsB,SAASC,qBAAoB,kBAAkB;AACvE,SAAO,SAAU,OAAO;AACtB,WAA0B,oBAAc,MAAM,SAAS;AAAA,MACrD,WAAW;AAAA,IACb,GAAG,KAAK,CAAC;AAAA,EACX;AACF;AACA,IAAI,wBAAwB;AAE5B,IAAI,cAAc,CAAC,WAAW;AAA9B,IACE,aAAa,CAAC,UAAU;AAE1B,IAAI,yBAAyB,SAASC,wBAAuB,kBAAkB;AAC7E,SAAO,SAAU,OAAO;AACtB,WAAO,MAAM,UAA6B,oBAAc,uBAAuB,SAAS;AAAA,MACtF,WAAW;AAAA,IACb,GAAG,KAAK,CAAC,IAAuB,oBAAc,yBAAiB,SAAS;AAAA,MACtE,WAAW;AAAA,IACb,GAAG,KAAK,CAAC;AAAA,EACX;AACF;AACA,IAAI,wBAAwB,SAASC,uBAAsB,MAAM;AAC/D,MAAI,YAAY,KAAK,WACnB,YAAY,yBAAyB,MAAM,WAAW;AACxD,MAAI,aAAa,yBAAyB,SAAS;AACnD,SAA0B,oBAAc,yBAAiB,SAAS;AAAA,IAChE;AAAA,EACF,GAAG,UAAU,CAAC;AAChB;AACA,IAAI,2BAA2B,SAASC,0BAAyB,OAAO;AACtE,MAAI,WAAW,MAAM,UACnB,QAAQ,yBAAyB,OAAO,UAAU;AACpD,MAAI,UAAU,MAAM,SAClB,WAAW,MAAM,UACjB,aAAa,MAAM,YACnB,qBAAqB,MAAM,aAC3B,aAAa,mBAAmB,YAChC,2BAA2B,mBAAmB;AAChD,MAAI,gBAAY,uBAAS,WAAW,4BAA4B,QAAQ,GACtE,aAAa,eAAe,WAAW,CAAC,GACxC,iBAAiB,WAAW,CAAC,GAC7B,oBAAoB,WAAW,CAAC;AAClC,MAAI,iBAAa,uBAAS,KAAK,GAC7B,aAAa,eAAe,YAAY,CAAC,GACzC,gBAAgB,WAAW,CAAC,GAC5B,mBAAmB,WAAW,CAAC;AACjC,8BAAU,WAAY;AACpB,QAAI,YAAY,CAAC,gBAAgB;AAC/B,wBAAkB,IAAI;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,UAAU,cAAc,CAAC;AAC7B,8BAAU,WAAY;AACpB,QAAI,iBAAiB,CAAC,YAAY,gBAAgB;AAChD,wBAAkB,KAAK;AAAA,IACzB;AACA,qBAAiB,KAAK;AAAA,EACxB,GAAG,CAAC,eAAe,UAAU,cAAc,CAAC;AAC5C,MAAI,WAAW,SAASC,YAAW;AACjC,WAAO,iBAAiB,IAAI;AAAA,EAC9B;AACA,MAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,QAAI,WAA8B,qBAAe,KAAK,GAAG;AAEvD,UAAI,MAAM,SAAS,WAAW,YAAY;AACxC,eAA0B,mBAAa,OAAO;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,MAAM,SAAS,WAAW,eAAe,gBAAgB;AAC3D,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,eAAc,eAAc,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG;AAAA,IACnE,OAAO,eAAc,eAAc,CAAC,GAAG,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,KAAK,GAAG,CAAC,GAAG;AAAA,MACpH,SAAS,WAAW,YAAY,iBAAiB,SAAS;AAAA,IAC5D,CAAC;AAAA,EACH,CAAC;AACD,MAAI,WAAW,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,IACzD,YAAY;AAAA,IACZ,UAAgB,eAAS,QAAQ,QAAQ,EAAE,IAAI,WAAW;AAAA,EAC5D,CAAC;AACD,SAAO;AACT;AACA,IAAI,2BAA2B;AAE/B,IAAI,YAAY,CAAC,SAAS,cAAc,eAAe,eAAe,gBAAgB;AACtF,IAAI,eAAe,SAASC,gBAAe;AACzC,MAAI,qBAAqB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC9F,MAAI,aAAa,kBAAkB;AAAA,IACjC,YAAY;AAAA,EACd,CAAC;AACD,MAAIC,SAAQ,WAAW,OACrBC,cAAa,WAAW,YACxBC,eAAc,WAAW,aACzBC,eAAc,WAAW,aACzBC,kBAAiB,WAAW,gBAC5B,OAAO,yBAAyB,YAAY,SAAS;AACvD,SAAO,eAAc;AAAA,IACnB,OAAO,gBAAgBJ,MAAK;AAAA,IAC5B,YAAY,qBAAqBC,WAAU;AAAA,IAC3C,aAAa,sBAAsBC,YAAW;AAAA,IAC9C,aAAa,sBAAsBC,YAAW;AAAA,IAC9C,gBAAgB,yBAAyBC,eAAc;AAAA,EACzD,GAAG,IAAI;AACT;AACA,IAAI,qBAAqB,aAAa;AACtC,IAAI,QAAQ,mBAAmB;AAC/B,IAAI,aAAa,mBAAmB;AACpC,IAAI,cAAc,mBAAmB;AACrC,IAAI,cAAc,mBAAmB;AACrC,IAAI,iBAAiB,mBAAmB;AACxC,IAAI,QAAQ,WAAW,YAAY;", "names": ["AnimatedInput", "Fade", "Collapse", "getStyleFromStatus", "AnimatedMultiValue", "AnimatedPlaceholder", "AnimatedSingleValue", "AnimatedValueContainer", "IsMultiValueContainer", "useIsMultiValueContainer", "onExited", "<PERSON><PERSON><PERSON><PERSON>", "makeAnimated", "Input", "MultiValue", "Placeholder", "SingleValue", "ValueContainer"]}