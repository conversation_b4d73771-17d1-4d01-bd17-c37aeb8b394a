import {
  TransitionGroup_default,
  Transition_default
} from "./chunk-KQDFNLUQ.js";
import {
  _objectSpread2,
  _objectWithoutProperties,
  _slicedToArray,
  defaultComponents,
  memoizeOne
} from "./chunk-KM3MICJN.js";
import {
  _extends
} from "./chunk-W6HCFRFS.js";
import {
  require_react_dom
} from "./chunk-FYDILROA.js";
import "./chunk-IKX2E5G2.js";
import "./chunk-XLOVNOK3.js";
import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/react-select/animated/dist/react-select-animated.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
var _excluded$4 = ["in", "onExited", "appear", "enter", "exit"];
var AnimatedInput = function AnimatedInput2(WrappedComponent) {
  return function(_ref) {
    _ref.in;
    _ref.onExited;
    _ref.appear;
    _ref.enter;
    _ref.exit;
    var props = _objectWithoutProperties(_ref, _excluded$4);
    return React.createElement(WrappedComponent, props);
  };
};
var AnimatedInput$1 = AnimatedInput;
var _excluded$3 = ["component", "duration", "in", "onExited"];
var Fade = function Fade2(_ref) {
  var Tag = _ref.component, _ref$duration = _ref.duration, duration = _ref$duration === void 0 ? 1 : _ref$duration, inProp = _ref.in;
  _ref.onExited;
  var props = _objectWithoutProperties(_ref, _excluded$3);
  var nodeRef = (0, import_react.useRef)(null);
  var transition = {
    entering: {
      opacity: 0
    },
    entered: {
      opacity: 1,
      transition: "opacity ".concat(duration, "ms")
    },
    exiting: {
      opacity: 0
    },
    exited: {
      opacity: 0
    }
  };
  return React.createElement(Transition_default, {
    mountOnEnter: true,
    unmountOnExit: true,
    in: inProp,
    timeout: duration,
    nodeRef
  }, function(state) {
    var innerProps = {
      style: _objectSpread2({}, transition[state]),
      ref: nodeRef
    };
    return React.createElement(Tag, _extends({
      innerProps
    }, props));
  });
};
var collapseDuration = 260;
var Collapse = function Collapse2(_ref2) {
  var children = _ref2.children, _in = _ref2.in, _onExited = _ref2.onExited;
  var ref = (0, import_react.useRef)(null);
  var _useState = (0, import_react.useState)("auto"), _useState2 = _slicedToArray(_useState, 2), width = _useState2[0], setWidth = _useState2[1];
  (0, import_react.useEffect)(function() {
    var el = ref.current;
    if (!el) return;
    var rafId = window.requestAnimationFrame(function() {
      return setWidth(el.getBoundingClientRect().width);
    });
    return function() {
      return window.cancelAnimationFrame(rafId);
    };
  }, []);
  var getStyleFromStatus = function getStyleFromStatus2(status) {
    switch (status) {
      default:
        return {
          width
        };
      case "exiting":
        return {
          width: 0,
          transition: "width ".concat(collapseDuration, "ms ease-out")
        };
      case "exited":
        return {
          width: 0
        };
    }
  };
  return React.createElement(Transition_default, {
    enter: false,
    mountOnEnter: true,
    unmountOnExit: true,
    in: _in,
    onExited: function onExited() {
      var el = ref.current;
      if (!el) return;
      _onExited === null || _onExited === void 0 ? void 0 : _onExited(el);
    },
    timeout: collapseDuration,
    nodeRef: ref
  }, function(status) {
    return React.createElement("div", {
      ref,
      style: _objectSpread2({
        overflow: "hidden",
        whiteSpace: "nowrap"
      }, getStyleFromStatus(status))
    }, children);
  });
};
var _excluded$2 = ["in", "onExited"];
var AnimatedMultiValue = function AnimatedMultiValue2(WrappedComponent) {
  return function(_ref) {
    var inProp = _ref.in, onExited = _ref.onExited, props = _objectWithoutProperties(_ref, _excluded$2);
    return React.createElement(Collapse, {
      in: inProp,
      onExited
    }, React.createElement(WrappedComponent, _extends({
      cropWithEllipsis: inProp
    }, props)));
  };
};
var AnimatedMultiValue$1 = AnimatedMultiValue;
var AnimatedPlaceholder = function AnimatedPlaceholder2(WrappedComponent) {
  return function(props) {
    return React.createElement(Fade, _extends({
      component: WrappedComponent,
      duration: props.isMulti ? collapseDuration : 1
    }, props));
  };
};
var AnimatedPlaceholder$1 = AnimatedPlaceholder;
var AnimatedSingleValue = function AnimatedSingleValue2(WrappedComponent) {
  return function(props) {
    return React.createElement(Fade, _extends({
      component: WrappedComponent
    }, props));
  };
};
var AnimatedSingleValue$1 = AnimatedSingleValue;
var _excluded$1 = ["component"];
var _excluded2 = ["children"];
var AnimatedValueContainer = function AnimatedValueContainer2(WrappedComponent) {
  return function(props) {
    return props.isMulti ? React.createElement(IsMultiValueContainer, _extends({
      component: WrappedComponent
    }, props)) : React.createElement(TransitionGroup_default, _extends({
      component: WrappedComponent
    }, props));
  };
};
var IsMultiValueContainer = function IsMultiValueContainer2(_ref) {
  var component = _ref.component, restProps = _objectWithoutProperties(_ref, _excluded$1);
  var multiProps = useIsMultiValueContainer(restProps);
  return React.createElement(TransitionGroup_default, _extends({
    component
  }, multiProps));
};
var useIsMultiValueContainer = function useIsMultiValueContainer2(_ref2) {
  var children = _ref2.children, props = _objectWithoutProperties(_ref2, _excluded2);
  var isMulti = props.isMulti, hasValue = props.hasValue, innerProps = props.innerProps, _props$selectProps = props.selectProps, components = _props$selectProps.components, controlShouldRenderValue = _props$selectProps.controlShouldRenderValue;
  var _useState = (0, import_react.useState)(isMulti && controlShouldRenderValue && hasValue), _useState2 = _slicedToArray(_useState, 2), cssDisplayFlex = _useState2[0], setCssDisplayFlex = _useState2[1];
  var _useState3 = (0, import_react.useState)(false), _useState4 = _slicedToArray(_useState3, 2), removingValue = _useState4[0], setRemovingValue = _useState4[1];
  (0, import_react.useEffect)(function() {
    if (hasValue && !cssDisplayFlex) {
      setCssDisplayFlex(true);
    }
  }, [hasValue, cssDisplayFlex]);
  (0, import_react.useEffect)(function() {
    if (removingValue && !hasValue && cssDisplayFlex) {
      setCssDisplayFlex(false);
    }
    setRemovingValue(false);
  }, [removingValue, hasValue, cssDisplayFlex]);
  var onExited = function onExited2() {
    return setRemovingValue(true);
  };
  var childMapper = function childMapper2(child) {
    if (isMulti && React.isValidElement(child)) {
      if (child.type === components.MultiValue) {
        return React.cloneElement(child, {
          onExited
        });
      }
      if (child.type === components.Placeholder && cssDisplayFlex) {
        return null;
      }
    }
    return child;
  };
  var newInnerProps = _objectSpread2(_objectSpread2({}, innerProps), {}, {
    style: _objectSpread2(_objectSpread2({}, innerProps === null || innerProps === void 0 ? void 0 : innerProps.style), {}, {
      display: isMulti && hasValue || cssDisplayFlex ? "flex" : "grid"
    })
  });
  var newProps = _objectSpread2(_objectSpread2({}, props), {}, {
    innerProps: newInnerProps,
    children: React.Children.toArray(children).map(childMapper)
  });
  return newProps;
};
var AnimatedValueContainer$1 = AnimatedValueContainer;
var _excluded = ["Input", "MultiValue", "Placeholder", "SingleValue", "ValueContainer"];
var makeAnimated = function makeAnimated2() {
  var externalComponents = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
  var components = defaultComponents({
    components: externalComponents
  });
  var Input2 = components.Input, MultiValue2 = components.MultiValue, Placeholder2 = components.Placeholder, SingleValue2 = components.SingleValue, ValueContainer2 = components.ValueContainer, rest = _objectWithoutProperties(components, _excluded);
  return _objectSpread2({
    Input: AnimatedInput$1(Input2),
    MultiValue: AnimatedMultiValue$1(MultiValue2),
    Placeholder: AnimatedPlaceholder$1(Placeholder2),
    SingleValue: AnimatedSingleValue$1(SingleValue2),
    ValueContainer: AnimatedValueContainer$1(ValueContainer2)
  }, rest);
};
var AnimatedComponents = makeAnimated();
var Input = AnimatedComponents.Input;
var MultiValue = AnimatedComponents.MultiValue;
var Placeholder = AnimatedComponents.Placeholder;
var SingleValue = AnimatedComponents.SingleValue;
var ValueContainer = AnimatedComponents.ValueContainer;
var index = memoizeOne(makeAnimated);
export {
  Input,
  MultiValue,
  Placeholder,
  SingleValue,
  ValueContainer,
  index as default
};
//# sourceMappingURL=react-select_animated.js.map
