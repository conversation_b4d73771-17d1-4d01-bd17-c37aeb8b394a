/* ViewStatus Component Styles */

/* Status Table Specific Styles */
.status-table tbody tr {
  height: 60px;
}

.status-table tbody td {
  vertical-align: middle !important;
}

.status-table .table-buttons-edit,
.status-table .table-buttons-delete,
.status-table .table-buttons-view {
  margin: 0 auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Status Page Header */
.status-page-header {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #545f71;
}

/* Status Table Cells */
.status-table-cell {
  color: #545f71;
}

.status-table-cell-checkbox {
  width: 40px;
  text-align: center;
  vertical-align: middle;
}

.status-table-cell-name {
  width: 25%;
  color: #545f71;
  vertical-align: middle;
}

.status-table-cell-type {
  width: 15%;
  color: #545f71;
  vertical-align: middle;
}

.status-table-cell-notes {
  width: 30%;
  color: #545f71;
  vertical-align: middle;
}

.status-table-cell-count {
  width: 10%;
  color: #545f71;
  vertical-align: middle;
}

.status-table-cell-action {
  width: 40px;
  text-align: center;
  vertical-align: middle !important;
  padding: 8px 12px;
}

/* Ensure action buttons are properly centered */
.status-table-cell-action > * {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}

/* Status Table Headers */
.status-table-header {
  width: 40px;
  text-align: center !important;
  padding-left: 12px;
  padding-right: 12px;
  vertical-align: middle;
}

.status-table-header-checkbox {
  width: 40px;
  text-align: center;
  vertical-align: middle;
}

.status-table-header-name {
  width: 25%;
  text-align: left;
  vertical-align: middle;
}

.status-table-header-type {
  width: 15%;
  text-align: left;
  vertical-align: middle;
}

.status-table-header-notes {
  width: 30%;
  text-align: left;
  vertical-align: middle;
}

.status-table-header-count {
  width: 10%;
  text-align: left;
  vertical-align: middle;
}

/* Status Search Form */
.status-search-form {
  margin-right: 10px;
}

.status-search-input {
  /* Inherits from search-input class */
}

/* Status Pagination */
.status-pagination-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 16px 34px;
  border-top: 1px solid #d3d3d3;
}

.status-pagination-left {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #545f71;
}

.status-pagination-text {
  color: #545f71;
}

.status-pagination-select {
  color: #545f71;
}

.status-pagination-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-prev-btn {
  color: #545f71;
  border: 1px solid #dee2e6;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.status-next-btn {
  color: #545f71;
  border: 1px solid #dee2e6;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.status-page-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  font-size: 14px;
}

/* Status Table Container */
.status-table-container {
  border-radius: 0;
  overflow: hidden;
}

/* Status Top Section */
.status-top-section {
  /* Inherits from existing styles */
}

.status-top-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-top-section-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}